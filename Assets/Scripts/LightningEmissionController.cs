using UnityEngine;

/// <summary>
/// Controls the Lightning particle system emission based on player position relative to lightning layer.
/// Emission lerps from 0-5 based on proximity to the main lightning layer.
/// </summary>
public class LightningEmissionController : MonoBehaviour
{
    [Header("Lightning Emission Settings")]
    [Tooltip("Maximum emission rate when fully inside lightning layer")]
    [SerializeField] private float maxEmissionRate = 5f;
    
    [Tooltip("Minimum emission rate when outside lightning layers")]
    [SerializeField] private float minEmissionRate = 0f;
    
    [Tooltip("How smoothly the emission rate changes")]
    [SerializeField] private float emissionTransitionSpeed = 2f;
    
    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    
    [Header("Debug")]
    [Tooltip("Enable debug logging for emission changes")]
    [SerializeField] private bool debugEmission = false;
    
    private ParticleSystem lightningParticleSystem;
    private ParticleSystem.EmissionModule emissionModule;
    private Transform player;
    private EnvironmentManager environmentManager;
    
    // Current emission state
    private float currentEmissionRate = 0f;
    private float targetEmissionRate = 0f;
    
    void Awake()
    {
        // Get the particle system component
        lightningParticleSystem = GetComponent<ParticleSystem>();
        if (lightningParticleSystem == null)
        {
            Debug.LogError("LightningEmissionController: No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }
        
        // Get the emission module
        emissionModule = lightningParticleSystem.emission;
        
        // Initialize emission rate
        currentEmissionRate = minEmissionRate;
        targetEmissionRate = minEmissionRate;
        emissionModule.rateOverTime = currentEmissionRate;
    }
    
    void Start()
    {
        // Get references
        environmentManager = EnvironmentManager.Instance;
        if (environmentManager == null)
        {
            Debug.LogError("LightningEmissionController: EnvironmentManager instance not found!");
            enabled = false;
            return;
        }
        
        // Get player reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.player != null)
        {
            player = GameManager.Instance.player.transform;
        }
        else
        {
            Debug.LogError("LightningEmissionController: Player reference not found in GameManager!");
            enabled = false;
            return;
        }
    }
    
    void Update()
    {
        if (player == null || environmentManager == null) return;
        
        // Calculate target emission rate based on player position
        CalculateTargetEmissionRate();
        
        // Smoothly transition to target emission rate
        UpdateEmissionRate();
    }
    
    void CalculateTargetEmissionRate()
    {
        float playerY = player.position.y;
        
        // Get lightning layer boundaries from EnvironmentManager
        float lightningTransitionStartY = environmentManager.LightningTransitionStartY;
        float lightningLayerStartY = environmentManager.LightningLayerStartY;
        float lightningLayerEndY = environmentManager.LightningLayerEndY;
        float clearCloudTransitionEndY = environmentManager.ClearCloudTransitionEndY;
        
        float newTargetEmissionRate = minEmissionRate;
        
        if (playerY >= lightningLayerStartY && playerY <= lightningLayerEndY)
        {
            // Player is fully inside the lightning layer - maximum emission
            newTargetEmissionRate = maxEmissionRate;
            
            if (debugEmission)
                Debug.Log($"Lightning: Player in main lightning layer (Y: {playerY:F1}), emission: {maxEmissionRate}");
        }
        else if (playerY >= lightningTransitionStartY && playerY < lightningLayerStartY)
        {
            // Player is in bottom transition zone - lerp from 0 to max
            float transitionFactor = Mathf.InverseLerp(lightningTransitionStartY, lightningLayerStartY, playerY);
            newTargetEmissionRate = Mathf.Lerp(minEmissionRate, maxEmissionRate, transitionFactor);
            
            if (debugEmission)
                Debug.Log($"Lightning: Player in bottom transition (Y: {playerY:F1}), factor: {transitionFactor:F2}, emission: {newTargetEmissionRate:F2}");
        }
        else if (playerY > lightningLayerEndY && playerY <= clearCloudTransitionEndY)
        {
            // Player is in top transition zone - lerp from max to 0
            float transitionFactor = Mathf.InverseLerp(lightningLayerEndY, clearCloudTransitionEndY, playerY);
            newTargetEmissionRate = Mathf.Lerp(maxEmissionRate, minEmissionRate, transitionFactor);
            
            if (debugEmission)
                Debug.Log($"Lightning: Player in top transition (Y: {playerY:F1}), factor: {transitionFactor:F2}, emission: {newTargetEmissionRate:F2}");
        }
        else
        {
            // Player is outside all lightning layers - no emission
            newTargetEmissionRate = minEmissionRate;
            
            if (debugEmission && targetEmissionRate != minEmissionRate)
                Debug.Log($"Lightning: Player outside lightning layers (Y: {playerY:F1}), emission: {minEmissionRate}");
        }
        
        targetEmissionRate = newTargetEmissionRate;
    }
    
    void UpdateEmissionRate()
    {
        // Smoothly transition current emission rate towards target
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        currentEmissionRate = Mathf.Lerp(currentEmissionRate, targetEmissionRate, clampedDeltaTime * emissionTransitionSpeed);
        
        // Apply the emission rate to the particle system
        emissionModule.rateOverTime = currentEmissionRate;
    }
    
    /// <summary>
    /// Public method to get current emission rate for debugging or other systems
    /// </summary>
    public float GetCurrentEmissionRate()
    {
        return currentEmissionRate;
    }
    
    /// <summary>
    /// Public method to get target emission rate for debugging or other systems
    /// </summary>
    public float GetTargetEmissionRate()
    {
        return targetEmissionRate;
    }
    
    /// <summary>
    /// Public method to manually set max emission rate at runtime
    /// </summary>
    public void SetMaxEmissionRate(float newMaxRate)
    {
        maxEmissionRate = newMaxRate;
    }
    
    #if UNITY_EDITOR
    void OnDrawGizmosSelected()
    {
        if (environmentManager == null) return;
        
        // Draw lightning layer boundaries for debugging
        Gizmos.color = Color.yellow;
        float gizmoSize = 2f;
        
        // Lightning transition start
        Vector3 transitionStartPos = new Vector3(transform.position.x, environmentManager.LightningTransitionStartY, transform.position.z);
        Gizmos.DrawWireCube(transitionStartPos, Vector3.one * gizmoSize);
        
        // Lightning layer start
        Gizmos.color = Color.red;
        Vector3 layerStartPos = new Vector3(transform.position.x, environmentManager.LightningLayerStartY, transform.position.z);
        Gizmos.DrawWireCube(layerStartPos, Vector3.one * gizmoSize);
        
        // Lightning layer end
        Vector3 layerEndPos = new Vector3(transform.position.x, environmentManager.LightningLayerEndY, transform.position.z);
        Gizmos.DrawWireCube(layerEndPos, Vector3.one * gizmoSize);
        
        // Clear cloud transition end
        Gizmos.color = Color.cyan;
        Vector3 transitionEndPos = new Vector3(transform.position.x, environmentManager.ClearCloudTransitionEndY, transform.position.z);
        Gizmos.DrawWireCube(transitionEndPos, Vector3.one * gizmoSize);
    }
    #endif
}
