using UnityEngine;

/// <summary>
/// Controls the Lightning particle system emission based on player position within the lightning layer.
/// Lightning only emits when player is fully inside the lightning layer (not in transition areas).
/// Emission lerps from 1-5 based on distance to the vertical center of the lightning layer.
/// </summary>
public class LightningEmissionController : MonoBehaviour
{
    [Header("Lightning Emission Settings")]
    [Tooltip("Maximum emission rate when at center of lightning layer")]
    [SerializeField] private float maxEmissionRate = 5f;

    [Tooltip("Minimum emission rate when at edges of lightning layer")]
    [SerializeField] private float minEmissionRate = 1f;

    [Tooltip("Emission rate when outside lightning layer")]
    [SerializeField] private float outsideEmissionRate = 0f;
    
    [Tooltip("How smoothly the emission rate changes")]
    [SerializeField] private float emissionTransitionSpeed = 2f;
    
    [Header("Performance")]
    [Tooltip("Maximum deltaTime to prevent large jumps during FPS drops")]
    [SerializeField] private float maxDeltaTime = 0.05f;
    
    [Header("Debug")]
    [Tooltip("Enable debug logging for emission changes")]
    [SerializeField] private bool debugEmission = false;
    
    private ParticleSystem lightningParticleSystem;
    private ParticleSystem.EmissionModule emissionModule;
    private Transform player;
    private EnvironmentManager environmentManager;
    
    // Current emission state
    private float currentEmissionRate = 0f;
    private float targetEmissionRate = 0f;
    
    void Awake()
    {
        // Get the particle system component
        lightningParticleSystem = GetComponent<ParticleSystem>();
        if (lightningParticleSystem == null)
        {
            Debug.LogError("LightningEmissionController: No ParticleSystem found on this GameObject!");
            enabled = false;
            return;
        }
        
        // Get the emission module
        emissionModule = lightningParticleSystem.emission;
        
        // Initialize emission rate
        currentEmissionRate = outsideEmissionRate;
        targetEmissionRate = outsideEmissionRate;
        emissionModule.rateOverTime = currentEmissionRate;
    }
    
    void Start()
    {
        // Get references
        environmentManager = EnvironmentManager.Instance;
        if (environmentManager == null)
        {
            Debug.LogError("LightningEmissionController: EnvironmentManager instance not found!");
            enabled = false;
            return;
        }
        
        // Get player reference from GameManager
        if (GameManager.Instance != null && GameManager.Instance.player != null)
        {
            player = GameManager.Instance.player.transform;
        }
        else
        {
            Debug.LogError("LightningEmissionController: Player reference not found in GameManager!");
            enabled = false;
            return;
        }
    }
    
    void Update()
    {
        if (player == null || environmentManager == null) return;
        
        // Calculate target emission rate based on player position
        CalculateTargetEmissionRate();
        
        // Smoothly transition to target emission rate
        UpdateEmissionRate();
    }
    
    void CalculateTargetEmissionRate()
    {
        float playerY = player.position.y;

        // Get lightning layer boundaries from EnvironmentManager
        float lightningLayerStartY = environmentManager.LightningLayerStartY;
        float lightningLayerEndY = environmentManager.LightningLayerEndY;

        float newTargetEmissionRate = outsideEmissionRate;

        if (playerY >= lightningLayerStartY && playerY <= lightningLayerEndY)
        {
            // Player is fully inside the lightning layer
            // Calculate distance to vertical center of lightning layer
            float lightningLayerCenterY = (lightningLayerStartY + lightningLayerEndY) / 2f;
            float lightningLayerHeight = lightningLayerEndY - lightningLayerStartY;

            // Calculate distance from center (0 = at center, 1 = at edge)
            float distanceFromCenter = Mathf.Abs(playerY - lightningLayerCenterY) / (lightningLayerHeight / 2f);

            // Clamp to ensure we don't go outside 0-1 range
            distanceFromCenter = Mathf.Clamp01(distanceFromCenter);

            // Lerp from max emission (at center) to min emission (at edges)
            // Invert the distance so 0 distance = max emission, 1 distance = min emission
            newTargetEmissionRate = Mathf.Lerp(maxEmissionRate, minEmissionRate, distanceFromCenter);

            if (debugEmission)
                Debug.Log($"Lightning: Player in lightning layer (Y: {playerY:F1}), center: {lightningLayerCenterY:F1}, distance from center: {distanceFromCenter:F2}, emission: {newTargetEmissionRate:F2}");
        }
        else
        {
            // Player is outside the lightning layer - no emission
            newTargetEmissionRate = outsideEmissionRate;

            if (debugEmission && targetEmissionRate != outsideEmissionRate)
                Debug.Log($"Lightning: Player outside lightning layer (Y: {playerY:F1}), emission: {outsideEmissionRate}");
        }

        targetEmissionRate = newTargetEmissionRate;
    }
    
    void UpdateEmissionRate()
    {
        // Smoothly transition current emission rate towards target
        float clampedDeltaTime = Mathf.Min(Time.deltaTime, maxDeltaTime);
        currentEmissionRate = Mathf.Lerp(currentEmissionRate, targetEmissionRate, clampedDeltaTime * emissionTransitionSpeed);
        
        // Apply the emission rate to the particle system
        emissionModule.rateOverTime = currentEmissionRate;
    }
    
    /// <summary>
    /// Public method to get current emission rate for debugging or other systems
    /// </summary>
    public float GetCurrentEmissionRate()
    {
        return currentEmissionRate;
    }
    
    /// <summary>
    /// Public method to get target emission rate for debugging or other systems
    /// </summary>
    public float GetTargetEmissionRate()
    {
        return targetEmissionRate;
    }
    
    /// <summary>
    /// Public method to manually set max emission rate at runtime
    /// </summary>
    public void SetMaxEmissionRate(float newMaxRate)
    {
        maxEmissionRate = newMaxRate;
    }
    
    #if UNITY_EDITOR
    void OnDrawGizmosSelected()
    {
        if (environmentManager == null) return;

        // Draw lightning layer boundaries for debugging
        float gizmoSize = 2f;

        // Lightning layer start (bottom boundary)
        Gizmos.color = Color.red;
        Vector3 layerStartPos = new Vector3(transform.position.x, environmentManager.LightningLayerStartY, transform.position.z);
        Gizmos.DrawWireCube(layerStartPos, Vector3.one * gizmoSize);

        // Lightning layer center
        Gizmos.color = Color.yellow;
        float centerY = (environmentManager.LightningLayerStartY + environmentManager.LightningLayerEndY) / 2f;
        Vector3 centerPos = new Vector3(transform.position.x, centerY, transform.position.z);
        Gizmos.DrawWireSphere(centerPos, gizmoSize);

        // Lightning layer end (top boundary)
        Gizmos.color = Color.red;
        Vector3 layerEndPos = new Vector3(transform.position.x, environmentManager.LightningLayerEndY, transform.position.z);
        Gizmos.DrawWireCube(layerEndPos, Vector3.one * gizmoSize);
    }
    #endif
}
