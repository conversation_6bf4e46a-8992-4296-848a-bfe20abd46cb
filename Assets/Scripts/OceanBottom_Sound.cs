using UnityEngine;
using FMODUnity;

public class AmbientSoundSpawner : MonoBehaviour
{
    [SerializeField] private EventReference soundEvent;

    [Header("Settings")]
    [SerializeField] private Transform player;
    [SerializeField] private float triggerDistance = 18f;
    [SerializeField] private float soundRadius = 10f;
    [SerializeField] private float spawnRatePerSecond = 6f;

    private float timer;

    private float oceanBottomY;
    private float cloudsBottomY;
    private bool boundariesCached = false;

    private float updateInterval = 0.5f;
    private float updateTimer = 0f;

    private void CacheBoundaries()
    {
        if (boundariesCached) return;
        if (GameManager.Instance != null)
        {
            if (GameManager.Instance.oceanBottom != null)
                oceanBottomY = GameManager.Instance.oceanBottom.position.y;
            if (GameManager.Instance.cloudsBottom != null)
                cloudsBottomY = GameManager.Instance.cloudsBottom.position.y;
            boundariesCached = true;
        }
    }

    private void Update()
    {
        if (player == null) return;
        updateTimer += Time.deltaTime;
        if (updateTimer < updateInterval) return;
        updateTimer = 0f;
        CacheBoundaries();
        if (!boundariesCached) return;

        // Clamp Y between oceanBottom and cloudsBottom
        float clampedY = Mathf.Clamp(player.position.y, oceanBottomY, cloudsBottomY);
        Vector3 newPosition = new Vector3(player.position.x, clampedY, player.position.z);
        transform.position = newPosition;

        float distance = Vector3.Distance(player.position, transform.position);

        // --- Lowpass logic ---
        bool playerInOcean = false;
        var pc = player.GetComponent<PlayerController>();
        if (pc != null)
            playerInOcean = pc.isInsideOcean;

        // Apply lowpass if player is in ocean OR if player is close to ocean boundary
        // This ensures remaining ocean sounds are filtered even when player just left ocean
        bool shouldApplyLowpass = playerInOcean || (player.position.y < cloudsBottomY + 5f);

        // Set FMOD parameter for lowpass (assume parameter name "OceanLowpass", 0 = off, 1 = on)
        AudioManager.SetGlobalParameter("OceanLowpass", shouldApplyLowpass ? 1f : 0f);

        // Only spawn sounds if player is within ocean boundaries (below cloudsBottom - 1)
        if (player.position.y >= cloudsBottomY - 1f) return;

        if (distance > triggerDistance) return;

        // Calculate spawn rate modifier based on player position in ocean
        float effectiveSpawnRate = spawnRatePerSecond;
        float topOceanZoneStart = cloudsBottomY - 6f; // 5 units below the stop point (cloudsBottomY - 1)

        if (player.position.y >= topOceanZoneStart)
        {
            // In top 5 units of ocean - reduce spawn rate
            float zoneProgress = (player.position.y - topOceanZoneStart) / 5f; // 0 to 1
            effectiveSpawnRate = spawnRatePerSecond * Mathf.Lerp(1f, 0.3f, zoneProgress); // Reduce to 30% at top
        }

        timer += updateInterval;
        if (timer >= 1f / effectiveSpawnRate)
        {
            timer = 0f;
            Vector2 randomCircle = Random.insideUnitCircle * soundRadius;
            Vector3 soundPos = new Vector3(transform.position.x + randomCircle.x, transform.position.y, transform.position.z + randomCircle.y);

            // Use bypass method to ensure ocean ambient sounds always play
            AudioManager.PlayOneShotBypassLOD(soundEvent, soundPos);
        }
    }
}
